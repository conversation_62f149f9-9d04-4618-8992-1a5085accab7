/**
 * 头像相关工具函数
 */

/**
 * 获取用户头像URL
 * @param avatarUrl 用户头像URL（可能是相对路径）
 * @returns 完整的头像URL或null
 */
export function getAvatarUrl(avatarUrl?: string | null): string | null {
  if (!avatarUrl) {
    return null;
  }
  
  // 如果已经是完整URL，直接返回
  if (avatarUrl.startsWith('http://') || avatarUrl.startsWith('https://')) {
    return avatarUrl;
  }
  
  // 如果是相对路径，确保以/开头
  const normalizedPath = avatarUrl.startsWith('/') ? avatarUrl : `/${avatarUrl}`;
  
  return normalizedPath;
}

/**
 * 检查头像URL是否有效
 * @param avatarUrl 头像URL
 * @returns 是否有效
 */
export function isValidAvatarUrl(avatarUrl?: string | null): boolean {
  return Boolean(avatarUrl && avatarUrl.trim().length > 0);
}

/**
 * 生成默认头像URL（使用用户名生成）
 * @param username 用户名
 * @returns 默认头像URL
 */
export function generateAvatarUrl(username: string): string {
  // 使用 DiceBear API 生成头像
  return `https://api.dicebear.com/7.x/initials/svg?seed=${encodeURIComponent(username)}`;
}
