@tailwind base;
@tailwind components;
@tailwind utilities;

/* 项目卡片优化样式 */
@layer components {
  .project-card-description {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
    line-height: 1.6;
  }

  /* 头部渐变背景（移除动画效果） */
  .project-card-gradient {
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%
    );
  }

  /* 全卡片渐变背景 - 增强边框效果 */
  .project-card-full-gradient {
    background: linear-gradient(135deg,
      #667eea 0%,
      #764ba2 25%,
      #f093fb 50%,
      #f5576c 75%,
      #4facfe 100%
    );
    position: relative;
    /* 添加渐变边框效果 */
    border: 2px solid transparent;
    background-clip: padding-box;
  }

  /* 渐变边框效果 */
  .project-card-full-gradient::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.8) 0%,
      rgba(118, 75, 162, 0.8) 25%,
      rgba(240, 147, 251, 0.8) 50%,
      rgba(245, 87, 108, 0.8) 75%,
      rgba(79, 172, 254, 0.8) 100%
    );
    border-radius: 0.75rem;
    z-index: -1;
    opacity: 0.6;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 边框发光效果 */
  .project-card-full-gradient::after {
    content: '';
    position: absolute;
    top: -4px;
    left: -4px;
    right: -4px;
    bottom: -4px;
    background: linear-gradient(135deg,
      rgba(102, 126, 234, 0.3) 0%,
      rgba(118, 75, 162, 0.3) 25%,
      rgba(240, 147, 251, 0.3) 50%,
      rgba(245, 87, 108, 0.3) 75%,
      rgba(79, 172, 254, 0.3) 100%
    );
    border-radius: 0.875rem;
    z-index: -2;
    opacity: 0;
    filter: blur(8px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* 统一的深色半透明覆盖层 - 占满100%宽高 */
  .project-card-unified-overlay {
    background: rgba(0, 0, 0, 0.25);
    backdrop-filter: blur(8px);
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);
    min-height: 240px;
    height: 100%;
    width: 100%;
    display: flex;
    flex-direction: column;
    position: absolute;
    top: 0;
    left: 0;
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .project-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.15),
      0 10px 10px -5px rgba(0, 0, 0, 0.08),
      0 0 30px rgba(102, 126, 234, 0.2);
  }

  /* 悬停时增强边框效果 */
  .project-card-hover:hover::before {
    opacity: 1;
    transform: scale(1.02);
  }

  .project-card-hover:hover::after {
    opacity: 0.8;
    transform: scale(1.05);
  }

  /* 在深色覆盖层上的文字样式优化 */
  .project-card-text-unified {
    color: rgba(255, 255, 255, 0.95);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.4);
  }

  /* 项目标题样式 */
  .project-card-title {
    color: rgba(255, 255, 255, 1);
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  }

  /* 项目描述区域 - 确保一致的高度和对齐 */
  .project-card-description-area {
    flex: 1;
    min-height: 20px;
    max-height: 20px;
    display: flex;
    align-items: flex-start;
    overflow: hidden;
  }

  /* 项目信息区域 - 固定位置确保对齐 */
  .project-card-info-area {
    margin-top: auto;
    padding-top: 0rem;
    min-height: 90px;
  }

  /* 项目卡片容器 - 确保一致的高度 */
  .project-card-container {
    height: 180px;
    position: relative;
    /* 添加微妙的外边框阴影 */
    filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.1));
  }

  /* 增强操作按钮的边框效果 */
  .project-card-action-button {
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-card-action-button:hover {
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 12px rgba(255, 255, 255, 0.2);
  }

  /* 搜索框边框美化 */
  .project-search-input {
    border: 2px solid rgba(229, 231, 235, 0.8);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-search-input:focus {
    border-color: rgba(59, 130, 246, 0.8);
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* 创建按钮边框美化 */
  .project-create-button {
    border: 1px solid rgba(37, 99, 235, 0.8);
    box-shadow:
      0 2px 4px rgba(37, 99, 235, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-create-button:hover {
    border-color: rgba(29, 78, 216, 1);
    box-shadow:
      0 4px 8px rgba(37, 99, 235, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 16px rgba(37, 99, 235, 0.2);
  }

  /* 模态框边框美化 */
  .project-modal {
    border: 2px solid rgba(229, 231, 235, 0.8);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 40px rgba(0, 0, 0, 0.1);
    backdrop-filter: blur(8px);
    border-radius: 0.75rem;
  }

  /* 模态框输入框美化 */
  .project-modal-input {
    border: 2px solid rgba(229, 231, 235, 0.8);
    box-shadow:
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-modal-input:focus {
    border-color: rgba(59, 130, 246, 0.8);
    box-shadow:
      0 0 0 3px rgba(59, 130, 246, 0.1),
      0 1px 3px rgba(0, 0, 0, 0.1),
      inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  /* 模态框按钮美化 */
  .project-modal-button-primary {
    border: 1px solid rgba(37, 99, 235, 0.8);
    box-shadow:
      0 2px 4px rgba(37, 99, 235, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-modal-button-primary:hover {
    border-color: rgba(29, 78, 216, 1);
    box-shadow:
      0 4px 8px rgba(37, 99, 235, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 16px rgba(37, 99, 235, 0.2);
  }

  .project-modal-button-secondary {
    border: 1px solid rgba(156, 163, 175, 0.8);
    box-shadow:
      0 2px 4px rgba(0, 0, 0, 0.1),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .project-modal-button-secondary:hover {
    border-color: rgba(107, 114, 128, 1);
    box-shadow:
      0 4px 8px rgba(0, 0, 0, 0.15),
      inset 0 1px 0 rgba(255, 255, 255, 0.3),
      0 0 12px rgba(0, 0, 0, 0.1);
  }

  /* 项目卡片宽度控制 */
  .project-cards-grid {
    /* CSS变量定义不同屏幕尺寸下的列数 */
    --cols-sm: 1;
    --cols-md: 2;
    --cols-lg: 3;
    --cols-xl: 3;
    --cols-2xl: 4;

    /* 卡片最小和最大宽度 */
    --card-min-width: 280px;
    --card-max-width: 400px;

    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(var(--cols-sm), 1fr);
  }

  /* 响应式断点 */
  @media (min-width: 768px) {
    .project-cards-grid {
      grid-template-columns: repeat(var(--cols-md), 1fr);
    }
  }

  @media (min-width: 1024px) {
    .project-cards-grid {
      grid-template-columns: repeat(var(--cols-lg), 1fr);
    }
  }

  @media (min-width: 1280px) {
    .project-cards-grid {
      grid-template-columns: repeat(var(--cols-xl), 1fr);
    }
  }

  @media (min-width: 1536px) {
    .project-cards-grid {
      grid-template-columns: repeat(var(--cols-2xl), 1fr);
    }
  }

  /* 自适应宽度方案 */
  .project-cards-grid-auto {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), 1fr));
  }

  /* 固定最大宽度方案 */
  .project-cards-grid-fixed {
    display: grid;
    gap: 1.5rem;
    grid-template-columns: repeat(auto-fit, minmax(var(--card-min-width), var(--card-max-width)));
    justify-content: center;
  }

  /* 文本截断样式 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }
}
