#!/usr/bin/env python3
"""
数据库迁移脚本：为projects表添加score和is_featured字段
"""

import sys
import os
sys.path.append('.')

from sqlalchemy import text
from app.db.session import engine, SessionLocal

def migrate_projects_table():
    """为projects表添加新字段"""
    db = SessionLocal()
    
    try:
        # 检查字段是否已存在
        result = db.execute(text("PRAGMA table_info(projects)"))
        columns = [row[1] for row in result.fetchall()]
        
        print("当前projects表字段:", columns)
        
        # 添加score字段
        if 'score' not in columns:
            print("添加score字段...")
            db.execute(text("ALTER TABLE projects ADD COLUMN score INTEGER DEFAULT 0"))
            db.execute(text("CREATE INDEX ix_projects_score ON projects (score)"))
            print("score字段添加成功")
        else:
            print("score字段已存在")
        
        # 添加is_featured字段
        if 'is_featured' not in columns:
            print("添加is_featured字段...")
            db.execute(text("ALTER TABLE projects ADD COLUMN is_featured BOOLEAN DEFAULT 0"))
            db.execute(text("CREATE INDEX ix_projects_is_featured ON projects (is_featured)"))
            print("is_featured字段添加成功")
        else:
            print("is_featured字段已存在")
        
        db.commit()
        print("数据库迁移完成")
        
        # 验证迁移结果
        result = db.execute(text("PRAGMA table_info(projects)"))
        columns = [row[1] for row in result.fetchall()]
        print("迁移后projects表字段:", columns)
        
    except Exception as e:
        print(f"迁移失败: {e}")
        db.rollback()
        raise
    finally:
        db.close()

if __name__ == "__main__":
    migrate_projects_table()
