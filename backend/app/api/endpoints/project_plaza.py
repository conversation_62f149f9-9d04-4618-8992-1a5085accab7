from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, desc
from typing import List

from app.db.session import get_db
from app.models.project import Project
from app.schemas.project import ProjectWithCreator

router = APIRouter()

@router.get("/", response_model=List[ProjectWithCreator])
async def get_plaza_projects(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取项目广场的项目列表（分数>20的项目，官方精选置顶，按分数倒序）"""
    
    # 先获取官方精选的高分项目
    featured_projects = db.query(Project).options(
        joinedload(Project.creator)
    ).filter(
        and_(Project.score > 20, Project.is_featured == True)
    ).order_by(desc(Project.score)).all()
    
    # 再获取非官方精选的高分项目
    regular_projects = db.query(Project).options(
        joinedload(Project.creator)
    ).filter(
        and_(Project.score > 20, Project.is_featured == False)
    ).order_by(desc(Project.score)).offset(skip).limit(limit).all()
    
    # 合并结果，官方精选在前
    all_projects = featured_projects + regular_projects
    
    # 转换为包含创建者信息的格式
    result = []
    for project in all_projects:
        project_dict = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "config_file_id": project.config_file_id,
            "creator_user_id": project.creator_user_id,
            "score": project.score,
            "is_featured": project.is_featured,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "creator_username": project.creator.username,
            "creator_avatar_url": project.creator.avatar_url
        }
        result.append(project_dict)
    
    return result

@router.get("/user/{user_id}", response_model=List[ProjectWithCreator])
async def get_user_high_score_projects(
    user_id: int,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db)
):
    """获取指定用户的高分项目（分数>20）"""
    
    projects = db.query(Project).options(
        joinedload(Project.creator)
    ).filter(
        and_(Project.creator_user_id == user_id, Project.score > 20)
    ).order_by(desc(Project.score)).offset(skip).limit(limit).all()
    
    # 转换为包含创建者信息的格式
    result = []
    for project in projects:
        project_dict = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "config_file_id": project.config_file_id,
            "creator_user_id": project.creator_user_id,
            "score": project.score,
            "is_featured": project.is_featured,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "creator_username": project.creator.username,
            "creator_avatar_url": project.creator.avatar_url
        }
        result.append(project_dict)
    
    return result
