from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session, joinedload
from typing import List, Optional
import os
from pathlib import Path

from app.core.auth import get_current_admin_user
from app.db.session import get_db
from app.models.user import User
from app.models.project import Project
from app.schemas.project import ProjectWithCreator, AdminProjectUpdate, ProjectDetailSchema

router = APIRouter()

# 配置文件上传目录
UPLOAD_DIR = Path("uploads/configs")

@router.get("/", response_model=List[ProjectWithCreator])
async def get_all_projects(
    skip: int = 0,
    limit: int = 100,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """管理员获取所有项目列表"""
    projects = db.query(Project).options(
        joinedload(Project.creator)
    ).offset(skip).limit(limit).all()
    
    # 转换为包含创建者信息的格式
    result = []
    for project in projects:
        project_dict = {
            "id": project.id,
            "name": project.name,
            "description": project.description,
            "config_file_id": project.config_file_id,
            "creator_user_id": project.creator_user_id,
            "score": project.score,
            "is_featured": project.is_featured,
            "created_at": project.created_at,
            "updated_at": project.updated_at,
            "creator_username": project.creator.username,
            "creator_avatar_url": project.creator.avatar_url
        }
        result.append(project_dict)
    
    return result

@router.get("/{project_id}", response_model=ProjectDetailSchema)
async def get_project_detail(
    project_id: int,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """管理员获取项目详情（包括配置文件内容）"""
    project = db.query(Project).options(
        joinedload(Project.creator)
    ).filter(Project.id == project_id).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # 读取配置文件内容
    config_file_content = None
    if project.config_file_id:
        config_file_path = UPLOAD_DIR / project.config_file_id
        if config_file_path.exists():
            try:
                with open(config_file_path, 'r', encoding='utf-8') as f:
                    config_file_content = f.read()
            except Exception as e:
                # 如果读取失败，记录错误但不影响其他信息的返回
                print(f"Failed to read config file {config_file_path}: {e}")
    
    project_dict = {
        "id": project.id,
        "name": project.name,
        "description": project.description,
        "config_file_id": project.config_file_id,
        "creator_user_id": project.creator_user_id,
        "score": project.score,
        "is_featured": project.is_featured,
        "created_at": project.created_at,
        "updated_at": project.updated_at,
        "creator_username": project.creator.username,
        "creator_avatar_url": project.creator.avatar_url,
        "config_file_content": config_file_content
    }
    
    return project_dict

@router.put("/{project_id}", response_model=ProjectWithCreator)
async def update_project_admin(
    project_id: int,
    project_update: AdminProjectUpdate,
    current_admin: User = Depends(get_current_admin_user),
    db: Session = Depends(get_db)
):
    """管理员更新项目（打分和标记官方精选）"""
    project = db.query(Project).options(
        joinedload(Project.creator)
    ).filter(Project.id == project_id).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # 更新字段
    if project_update.score is not None:
        project.score = project_update.score
    if project_update.is_featured is not None:
        project.is_featured = project_update.is_featured
    
    db.commit()
    db.refresh(project)
    
    project_dict = {
        "id": project.id,
        "name": project.name,
        "description": project.description,
        "config_file_id": project.config_file_id,
        "creator_user_id": project.creator_user_id,
        "score": project.score,
        "is_featured": project.is_featured,
        "created_at": project.created_at,
        "updated_at": project.updated_at,
        "creator_username": project.creator.username,
        "creator_avatar_url": project.creator.avatar_url
    }
    
    return project_dict
