from sqlalchemy import Column, Integer, String, Text, DateTime, ForeignKey, Boolean
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship

from app.db.session import Base

class Project(Base):
    __tablename__ = "projects"

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False, index=True)  # 项目名称
    description = Column(Text, nullable=True)  # 项目简介
    creator_user_id = Column(Integer, ForeignKey("users.id"), nullable=False)  # 创建者用户ID
    config_file_id = Column(String, nullable=True)  # 配置文件ID（上传后得到ID）
    score = Column(Integer, default=0, index=True)  # 管理员评分
    is_featured = Column(Boolean, default=False, index=True)  # 是否官方精选
    created_at = Column(DateTime(timezone=True), server_default=func.now())  # 创建时间
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())  # 最近更新时间

    # 关系
    creator = relationship("User", back_populates="projects")

    def __repr__(self):
        return f"<Project {self.name}>"