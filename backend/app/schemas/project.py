from pydantic import BaseModel
from datetime import datetime
from typing import Optional

class ProjectBase(BaseModel):
    name: str
    description: Optional[str] = None
    config_file_id: Optional[str] = None

class ProjectCreate(ProjectBase):
    pass

class ProjectUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    config_file_id: Optional[str] = None

class ProjectSchema(ProjectBase):
    id: int
    creator_user_id: int
    score: int = 0
    is_featured: bool = False
    created_at: datetime
    updated_at: datetime

    class Config:
        orm_mode = True

class ProjectWithCreator(ProjectSchema):
    creator_username: str
    creator_avatar_url: Optional[str] = None

    class Config:
        orm_mode = True

# 管理员项目管理相关Schema
class AdminProjectUpdate(BaseModel):
    score: Optional[int] = None
    is_featured: Optional[bool] = None

class ProjectDetailSchema(ProjectWithCreator):
    config_file_content: Optional[str] = None  # 配置文件内容

    class Config:
        orm_mode = True