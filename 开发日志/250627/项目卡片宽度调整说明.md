# 项目卡片宽度调整说明

## 🎯 功能概述

项目卡片现在支持多种布局模式，您可以根据需要调整卡片的宽度和排列方式。

## 📐 布局模式

### 1. 宽卡片布局（推荐）
- **特点**：卡片更宽，显示更多内容
- **适用场景**：项目描述较长，需要更好的阅读体验
- **布局**：
  - 小屏幕：1列
  - 大屏幕：2列
  - 超大屏幕：3列

### 2. 默认布局
- **特点**：平衡的卡片宽度
- **适用场景**：通用场景，适合大多数用户
- **布局**：
  - 小屏幕：1列
  - 中等屏幕：2列
  - 大屏幕：3列

### 3. 窄卡片布局
- **特点**：卡片较窄，可显示更多项目
- **适用场景**：项目数量多，需要快速浏览
- **布局**：
  - 小屏幕：1列
  - 中等屏幕：2列
  - 大屏幕：3列
  - 超大屏幕：4列
  - 2K屏幕：5列

### 4. 自适应布局
- **特点**：根据屏幕宽度自动调整列数
- **适用场景**：不同设备间的最佳适配
- **特性**：
  - 最小卡片宽度：280px
  - 最大卡片宽度：400px
  - 自动计算最佳列数

## 🛠️ 使用方法

### 在界面中切换
1. 打开个人项目页面
2. 在搜索框右侧找到布局选择器
3. 从下拉菜单中选择您喜欢的布局模式
4. 卡片布局会立即更新

### 代码中自定义
如果您需要更精细的控制，可以修改以下文件：

#### 1. 修改CSS变量（推荐）
在 `frontend/src/index.css` 中调整：

```css
.project-cards-grid-auto {
  --card-min-width: 300px;  /* 调整最小宽度 */
  --card-max-width: 450px;  /* 调整最大宽度 */
}
```

#### 2. 修改布局函数
在 `frontend/src/components/Projects.tsx` 中的 `getGridClassName` 函数中添加新的布局模式：

```typescript
case 'custom':
  return 'grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5';
```

#### 3. 添加新的布局选项
在布局选择器中添加新选项：

```tsx
<option value="custom">自定义布局</option>
```

## 🎨 样式特性

所有布局模式都保持以下特性：
- ✨ 渐变边框效果
- 🌟 悬停发光动画
- 🎭 立体阴影层次
- 📱 响应式设计
- 🔄 平滑过渡动画

## 📱 响应式断点

- **小屏幕**：< 768px
- **中等屏幕**：768px - 1024px
- **大屏幕**：1024px - 1280px
- **超大屏幕**：1280px - 1536px
- **2K屏幕**：> 1536px

## 💡 最佳实践

1. **宽卡片布局**：适合内容丰富的项目展示
2. **默认布局**：适合一般用途，平衡性最好
3. **窄卡片布局**：适合项目数量多的情况
4. **自适应布局**：适合多设备使用的场景

## 🔧 技术实现

- 使用CSS Grid布局系统
- CSS变量控制响应式断点
- React状态管理布局模式
- Tailwind CSS响应式类名
- 平滑的CSS过渡动画

## 📝 注意事项

1. 布局切换是实时的，无需刷新页面
2. 所有布局模式都保持卡片的功能完整性
3. 在小屏幕设备上，所有模式都会自动调整为单列布局
4. 自适应布局可能在某些屏幕尺寸下显示不规则的列数，这是正常现象
